---
- name: build binary
  ansible.builtin.shell:
    cmd: "npm run build:wikidata"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy binary
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/wikidata"
    dest: "{{ ansible_env.HOME }}/wikidata"
    recursive: true
    use_ssh_args: true
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: wikidata
    hour: "*"
    minute: "20"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ ansible_env.HOME }}/wikidata --disable-warning=ExperimentalWarning >> {{ wikidata_stdout_path }} 2>> {{ wikidata_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/9e1d8511-3d39-4e76-a774-e4c3ceca0fff"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: wikidata-logrotate-cron
    dest: "/etc/logrotate.d/cron-wikidata"
  become: true
  notify:
    - restart logrotate

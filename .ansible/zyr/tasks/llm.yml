---
- name: build binary
  ansible.builtin.shell:
    cmd: "npm run build:llm"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy binary
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/llm"
    dest: "{{ ansible_env.HOME }}/llm"
    recursive: true
    use_ssh_args: true
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: llm
    hour: "19,20,21,22,23,0,1,2"
    minute: "30"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} DEEPSEEK_API_KEY={{ deepseek_api_key }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ ansible_env.HOME }}/llm --disable-warning=ExperimentalWarning >> {{ llm_stdout_path }} 2>> {{ llm_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/bdbfb9a9-9d08-47e7-9098-926206c43792"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: llm-logrotate-cron.j2
    dest: "/etc/logrotate.d/cron-llm"
  become: true
  notify:
    - restart logrotate

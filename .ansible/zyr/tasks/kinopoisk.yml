---
- name: build binary
  ansible.builtin.shell:
    cmd: "npm run build:kinopoisk"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy binary
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/kinopoisk"
    dest: "{{ ansible_env.HOME }}/kinopoisk"
    recursive: true
    use_ssh_args: true
- name: create fast traverse cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: kinopoisk
    hour: "5-23"
    minute: "11"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} CHROME_EXECUTABLE_PATH={{ chrome_executable_path }} CHROME_USER_DATA_PATH={{ chrome_user_data_path }} KINANET_PDF_MARKS_DIRECTORY_PATH={{ kinanet_pdf_marks_directory_path }} TELEGRAM_CACHE_DIRECTORY_PATH={{ telegram_cache_directory_path }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ ansible_env.HOME }}/kinopoisk --disable-warning=ExperimentalWarning --keep-previous-friends >> {{ kinopoisk_stdout_path }} 2>> {{ kinopoisk_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/f86318c7-f93b-4d0a-8066-a565ba4456e3"
  become: true
- name: create full traverse cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: kinopoisk-full-traverse
    hour: "2"
    minute: "11"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} CHROME_EXECUTABLE_PATH={{ chrome_executable_path }} CHROME_USER_DATA_PATH={{ chrome_user_data_path }} KINANET_PDF_MARKS_DIRECTORY_PATH={{ kinanet_pdf_marks_directory_path }} TELEGRAM_CACHE_DIRECTORY_PATH={{ telegram_cache_directory_path }} NODE_OPTIONS=--max_old_space_size=512 {{ ansible_env.HOME }}/kinopoisk --disable-warning=ExperimentalWarning >> {{ kinopoisk_stdout_path }} 2>> {{ kinopoisk_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/f86318c7-f93b-4d0a-8066-a565ba4456e3"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: kinopoisk-logrotate-cron.j2
    dest: "/etc/logrotate.d/cron-kinopoisk"
  become: true
  notify:
    - restart logrotate

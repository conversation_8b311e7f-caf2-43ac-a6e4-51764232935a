---
- name: build binary
  ansible.builtin.shell:
    cmd: "npm run build:wikimedia"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy binary
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/wikimedia"
    dest: "{{ ansible_env.HOME }}/wikimedia"
    recursive: true
    use_ssh_args: true
- name: create wikimedia.sh
  ansible.builtin.copy:
    dest: "{{ ansible_env.HOME }}/wikimedia.sh"
    mode: "711"
    content: |
      POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} WIKIMEDIA_ACCESS_TOKEN={{ wikimedia_access_token }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ ansible_env.HOME }}/wikimedia --disable-warning=ExperimentalWarning >> {{ wikimedia_stdout_path }} 2>> {{ wikimedia_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/30df1323-3202-4f56-acbe-7a0798d5a3f4
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: wikimedia
    hour: "*"
    minute: "20"
    user: nikityy
    job: "{{ ansible_env.HOME }}/wikimedia.sh"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: wikimedia-logrotate-cron
    dest: "/etc/logrotate.d/cron-wikimedia"
  become: true
  notify:
    - restart logrotate

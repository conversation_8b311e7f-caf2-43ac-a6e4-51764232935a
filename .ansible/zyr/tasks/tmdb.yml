---
- name: build binary
  ansible.builtin.shell:
    cmd: "npm run build:tmdb"
  args:
    chdir: "{{ role_path }}/../../"
  delegate_to: localhost
- name: copy binary
  ansible.posix.synchronize:
    src: "{{ role_path }}/../../dist/tmdb"
    dest: "{{ ansible_env.HOME }}/tmdb"
    recursive: true
    use_ssh_args: true
- name: create cron task
  ansible.builtin.cron:
    cron_file: zyr
    name: tmdb
    hour: "*"
    minute: "20"
    user: nikityy
    job: "POSTGRES_CONNECTION_STRING={{ postgres_connection_string }} IMAGES_PATH={{ images_path }} TMDB_API_KEY={{ tmdb_api_key }} NODE_OPTIONS=--max_old_space_size=512 timeout 1h {{ ansible_env.HOME }}/tmdb --disable-warning=ExperimentalWarning >> {{ tmdb_stdout_path }} 2>> {{ tmdb_stderr_path }} && curl -fsS -m 10 --retry 5 -o /dev/null https://hc-ping.com/c7804c7c-889e-464e-9d6b-c77008a9f807"
  become: true
- name: copy logrotate config
  ansible.builtin.template:
    src: tmdb-logrotate-cron
    dest: "/etc/logrotate.d/cron-tmdb"
  become: true
  notify:
    - restart logrotate

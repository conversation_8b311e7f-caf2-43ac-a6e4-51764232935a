[Unit]
Description=Zyr Server
After=network.target

[Service]
ExecStart={{ binary_path }}
Environment=NODE_ENV=production
Environment=PORT={{ port }}
Environment=POSTGRES_CONNECTION_STRING={{ postgres_connection_string }}
Environment=COOKIE_SIGNING_KEY={{ cookie_signing_key }}
Environment=DEEPSEEK_API_KEY={{ deepseek_api_key }}
Environment=INDEX_HTML_PATH={{ index_html_path }}
Environment=ERROR_HTML_PATH={{ error_html_path }}
Restart=on-failure
User=nikityy

[Install]
WantedBy=multi-user.target
